# 园区资产高级查询组件

## 组件说明

`ParkAdvancedSearchDialog.vue` 是园区资产管理页面的高级查询弹窗组件，基于 `BaseDialog` 公共弹窗组件开发。

## 功能特性

1. **完整的字段覆盖**：包含园区资产表格中的所有字段，支持全面的筛选查询
2. **分组展示**：将字段按照逻辑分组（基本信息、数值信息、面积信息、联系信息、其他信息）
3. **响应式布局**：使用 Element UI 的栅格系统，支持不同屏幕尺寸
4. **数据过滤**：自动过滤空值，只传递有效的查询条件
5. **表单重置**：支持一键重置所有查询条件

## 使用方式

### 1. 在父组件中引入

```vue
<template>
  <!-- 高级查询弹窗组件 -->
  <park-advanced-search-dialog ref="parkAdvancedSearch" @search="handleAdvancedSearchSubmit" />
</template>

<script>
import ParkAdvancedSearchDialog from './components/ParkAdvancedSearchDialog.vue'

export default {
  components: {
    ParkAdvancedSearchDialog
  },
  methods: {
    // 打开高级查询弹窗
    handleAdvancedSearch() {
      this.$refs.parkAdvancedSearch.showDialog()
    },

    // 处理高级查询提交
    handleAdvancedSearchSubmit(searchParams) {
      // 合并高级查询参数到搜索表单
      this.searchForm = { ...this.searchForm, ...searchParams }
      this.currentPage = 1
      this.fetchData()
    }
  }
}
</script>
```

### 2. 触发高级查询

```vue
<el-button type="primary" icon="el-icon-search" @click="handleAdvancedSearch">高级查询</el-button>
```

## 组件接口

### Props
无

### Events
- `search`: 查询事件，参数为过滤后的查询条件对象

### Methods
- `showDialog()`: 显示弹窗
- `closeDialog()`: 关闭弹窗

## 查询字段

### 基本信息
- 资产编号 (zpkAssetNumber)
- 资产名称 (zpkAssetName)
- 资产类型 (zpkAssetType)
- 现状用途 (zpkCurrentUsageDescription)
- 所在地区 (zpkLocationProvinceCityDistrict)
- 具体位置 (zpkSpecificLocation)
- 主要经营方向 (zpkMainBusinessDirection)
- 境内/境外 (zpkDomesticOrForeign)
- 是否两非资产 (zpkIsNonCoreAsset)
- 是否存在纠纷 (zpkHasDispute)
- 是否存在抵押 (zpkHasMortgage)

### 数值信息
- 占地面积 (zpkTotalArea)
- 原值 (zpkOriginalValue)
- 净值 (zpkNetValue)

### 面积信息
- 科研办公面积 (zpkResearchOfficeArea)
- 商业面积 (zpkCommercialArea)
- 住宅面积 (zpkResidentialArea)
- 工业面积 (zpkIndustrialArea)
- 出租面积 (zpkRentalArea)
- 自用面积 (zpkSelfUseArea)
- 闲置面积 (zpkIdleArea)
- 其他面积 (zpkOtherArea)
- 已抵押面积 (zpkMortgagedArea)

### 联系信息
- 联系人 (zpkOperator)
- 联系电话 (zpkOperatorContact)
- 部门负责人 (zpkDepartmentLeader)
- 部门电话 (zpkDepartmentLeaderContact)
- 分公司负责人 (zpkCompanyLeader)
- 分公司电话 (zpkCompanyLeaderContact)

### 其他信息
- 备注 (zpkRemarks)
- 创建人 (zpkCreatedBy)

## 样式特性

- 响应式设计，支持不同屏幕尺寸
- 分组标题带有蓝色下划线装饰
- 表单最大高度限制，超出时显示滚动条
- 统一的表单项间距和标签样式

## 注意事项

1. 组件基于 `BaseDialog` 开发，继承了其所有特性
2. 查询条件会自动过滤空值，只传递有效数据
3. 支持表单重置功能，可一键清空所有查询条件
4. 弹窗尺寸设置为 `Max`（90%宽度），适合大量字段展示
